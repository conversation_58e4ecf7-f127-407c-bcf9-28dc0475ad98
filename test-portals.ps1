Write-Host "=== Test des Portals Web ===" -ForegroundColor Cyan

# Test Main Web Portal
Write-Host "Test Main Web Portal (localhost:8080)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -Method GET -TimeoutSec 10
    Write-Host "SUCCESS: Main Web Portal accessible!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Main Web Portal inaccessible" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Admin Portal
Write-Host "Test Admin Portal (localhost:8081)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081" -Method GET -TimeoutSec 10
    Write-Host "SUCCESS: Admin Portal accessible!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Admin Portal inaccessible" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Seller Portal
Write-Host "Test Seller Portal (localhost:8082)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8082" -Method GET -TimeoutSec 10
    Write-Host "SUCCESS: Seller Portal accessible!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Seller Portal inaccessible" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== Test termine ===" -ForegroundColor Cyan

Write-Host ""
Write-Host "=== Instructions pour tester l'authentification ===" -ForegroundColor Yellow
Write-Host "1. Ouvrez http://localhost:8080 (Main Web)"
Write-Host "2. Ouvrez http://localhost:8081 (Admin Portal)"  
Write-Host "3. Ouvrez http://localhost:8082 (Seller Portal)"
Write-Host "4. Testez la connexion avec:"
Write-Host "   - Admin: <EMAIL> / Admin123!"
Write-Host "   - Seller: <EMAIL> / TestSeller123!"
Write-Host "   - User: <EMAIL> / TestUser123!"
