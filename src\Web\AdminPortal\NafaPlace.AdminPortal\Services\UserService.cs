using System.Net.Http.Json;
using System.Text.Json;
using System.Net.Http.Headers;
using NafaPlace.AdminPortal.Models.Users;
using NafaPlace.AdminPortal.Models.Common;
using Blazored.LocalStorage;

namespace NafaPlace.AdminPortal.Services;

public interface IUserService
{
    Task<PagedResult<UserDto>> GetUsersAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null);
    Task<UserDto?> GetUserByIdAsync(int id);
    Task<UserDto> CreateUserAsync(CreateUserRequest request);
    Task<UserDto> UpdateUserAsync(int id, UpdateUserRequest request);
    Task DeleteUserAsync(int id);
    Task<List<RoleDto>> GetRolesAsync();
    Task<RoleDto> CreateRoleAsync(CreateRoleRequest request);
    Task DeleteRoleAsync(int roleId);
    Task<bool> UpdateUserRolesAsync(int userId, List<string> roles);
    Task<RoleDto> UpdateRoleAsync(int id, UpdateRoleRequest request);
}

public class UserService : IUserService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly ILocalStorageService _localStorage;

    public UserService(HttpClient httpClient, ILocalStorageService localStorage)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<PagedResult<UserDto>> GetUsersAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return new PagedResult<UserDto>
                {
                    Items = new List<UserDto>(),
                    TotalCount = 0,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var query = $"?pageNumber={pageNumber}&pageSize={pageSize}";
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query += $"&searchTerm={Uri.EscapeDataString(searchTerm)}";
            }

            var response = await _httpClient.GetAsync($"/api/users{query}");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<PagedResult<UserDto>>(_jsonOptions);
                return result ?? new PagedResult<UserDto>
                {
                    Items = new List<UserDto>(),
                    TotalCount = 0,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };
            }

            return new PagedResult<UserDto>
            {
                Items = new List<UserDto>(),
                TotalCount = 0,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des utilisateurs: {ex.Message}");
            return new PagedResult<UserDto>
            {
                Items = new List<UserDto>(),
                TotalCount = 0,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
    }

    public async Task<UserDto?> GetUserByIdAsync(int id)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return null;
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.GetAsync($"/api/users/{id}");
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<UserDto>(_jsonOptions);
            }
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de l'utilisateur {id}: {ex.Message}");
            return null;
        }
    }

    public async Task<UserDto> CreateUserAsync(CreateUserRequest request)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PostAsJsonAsync("/api/users", request, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<UserDto>(_jsonOptions);
            return result ?? throw new Exception("Réponse invalide du serveur");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création de l'utilisateur: {ex.Message}");
            throw;
        }
    }

    public async Task<UserDto> UpdateUserAsync(int id, UpdateUserRequest request)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PutAsJsonAsync($"/api/users/{id}", request, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<UserDto>(_jsonOptions);
            return result ?? throw new Exception("Réponse invalide du serveur");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de l'utilisateur {id}: {ex.Message}");
            throw;
        }
    }

    public async Task DeleteUserAsync(int id)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.DeleteAsync($"/api/users/{id}");
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de l'utilisateur {id}: {ex.Message}");
            throw;
        }
    }

    public async Task<List<RoleDto>> GetRolesAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.GetAsync("/api/roles");
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<List<RoleDto>>(_jsonOptions);
            return result ?? new List<RoleDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des rôles: {ex.Message}");
            throw;
        }
    }

    public async Task<RoleDto> CreateRoleAsync(CreateRoleRequest request)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PostAsJsonAsync("/api/roles", request, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<RoleDto>(_jsonOptions);
            return result ?? throw new Exception("Réponse invalide du serveur");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création du rôle: {ex.Message}");
            throw;
        }
    }

    public async Task DeleteRoleAsync(int roleId)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.DeleteAsync($"/api/roles/{roleId}");
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression du rôle {roleId}: {ex.Message}");
            throw;
        }
    }

    public async Task<bool> UpdateUserRolesAsync(int userId, List<string> roles)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var request = new { Roles = roles };
            var response = await _httpClient.PutAsJsonAsync($"/api/users/{userId}/roles", request, _jsonOptions);
            response.EnsureSuccessStatusCode();

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour des rôles de l'utilisateur {userId}: {ex.Message}");
            throw;
        }
    }

    public async Task<RoleDto> UpdateRoleAsync(int id, UpdateRoleRequest request)
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PutAsJsonAsync($"/api/roles/{id}", request, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<RoleDto>(_jsonOptions);
            return result ?? throw new Exception("Réponse invalide du serveur");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du rôle {id}: {ex.Message}");
            throw;
        }
    }
}
