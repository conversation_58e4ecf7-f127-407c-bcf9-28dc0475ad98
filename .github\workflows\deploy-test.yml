name: Deploy to Test Environment (Fly.io)

on:
  push:
    branches: [ dev ]
  workflow_dispatch:

env:
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
  CLOUDINARY_API_KEY: ${{ secrets.CLOUDINARY_API_KEY }}
  CLOUDINARY_API_SECRET: ${{ secrets.CLOUDINARY_API_SECRET }}
  CLOUDINARY_CLOUD_NAME: ${{ secrets.CLOUDINARY_CLOUD_NAME }}
  STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
  STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
  JWT_SECRET: ${{ secrets.JWT_SECRET }}

jobs:
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 9.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --no-restore --configuration Release

    - name: Run tests
      run: dotnet test --no-build --verbosity normal --configuration Release

  deploy-apis:
    name: Deploy API Services
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/dev'

    strategy:
      matrix:
        service: [
          { name: 'identity-api', path: 'src/Services/Identity/NafaPlace.Identity.API', port: 8080 },
          { name: 'catalog-api', path: 'src/Services/Catalog/NafaPlace.Catalog.API', port: 8080 },
          { name: 'order-api', path: 'src/Services/Order/NafaPlace.Order.API', port: 8080 },
          { name: 'cart-api', path: 'src/Services/Cart/NafaPlace.Cart.API', port: 8080 },
          { name: 'reviews-api', path: 'src/Services/Reviews/NafaPlace.Reviews.API', port: 8080 },
          { name: 'notifications-api', path: 'src/Services/Notifications/NafaPlace.Notifications.API', port: 8080 },
          { name: 'payment-api', path: 'src/Services/Payment/NafaPlace.Payment.API', port: 8080 },
          { name: 'api-gateway', path: 'src/ApiGateway/NafaPlace.ApiGateway', port: 8080 }
        ]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create fly.toml for ${{ matrix.service.name }}
      run: |
        cd ${{ matrix.service.path }}
        cat > fly.toml << EOF
        app = "nafaplace-${{ matrix.service.name }}"
        primary_region = "cdg"

        [build]

        [env]
          ASPNETCORE_ENVIRONMENT = "Production"

        [http_service]
          internal_port = ${{ matrix.service.port }}
          force_https = true
          auto_stop_machines = true
          auto_start_machines = true
          min_machines_running = 0
          processes = ["app"]

        [[vm]]
          cpu_kind = "shared"
          cpus = 1
          memory_mb = 512
        EOF

    - name: Set secrets for ${{ matrix.service.name }}
      run: |
        cd ${{ matrix.service.path }}
        flyctl secrets set \
          CLOUDINARY_CLOUD_NAME="${{ env.CLOUDINARY_CLOUD_NAME }}" \
          CLOUDINARY_API_KEY="${{ env.CLOUDINARY_API_KEY }}" \
          CLOUDINARY_API_SECRET="${{ env.CLOUDINARY_API_SECRET }}" \
          STRIPE_PUBLISHABLE_KEY="${{ env.STRIPE_PUBLISHABLE_KEY }}" \
          STRIPE_SECRET_KEY="${{ env.STRIPE_SECRET_KEY }}" \
          JWT_SECRET="${{ env.JWT_SECRET }}" \
          --app nafaplace-${{ matrix.service.name }} || echo "App may not exist yet"

    - name: Deploy ${{ matrix.service.name }} to Fly.io
      run: |
        cd ${{ matrix.service.path }}
        flyctl deploy --remote-only --ha=false

  deploy-web-apps:
    name: Deploy Web Applications
    runs-on: ubuntu-latest
    needs: deploy-apis
    if: github.ref == 'refs/heads/dev'

    strategy:
      matrix:
        webapp: [
          { name: 'web-test', path: 'src/Web/NafaPlace.Web', port: 8080 },
          { name: 'admin-test', path: 'src/Web/AdminPortal/NafaPlace.AdminPortal', port: 8080 },
          { name: 'seller-test', path: 'src/Web/SellerPortal/NafaPlace.SellerPortal', port: 8080 }
        ]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create fly.toml for ${{ matrix.webapp.name }}
      run: |
        cd ${{ matrix.webapp.path }}
        cat > fly.toml << EOF
        app = "nafaplace-${{ matrix.webapp.name }}"
        primary_region = "cdg"

        [build]

        [env]
          ASPNETCORE_ENVIRONMENT = "Production"

        [http_service]
          internal_port = ${{ matrix.webapp.port }}
          force_https = true
          auto_stop_machines = true
          auto_start_machines = true
          min_machines_running = 0
          processes = ["app"]

        [[vm]]
          cpu_kind = "shared"
          cpus = 1
          memory_mb = 1024
        EOF

    - name: Set secrets for ${{ matrix.webapp.name }}
      run: |
        cd ${{ matrix.webapp.path }}
        flyctl secrets set \
          CLOUDINARY_CLOUD_NAME="${{ env.CLOUDINARY_CLOUD_NAME }}" \
          CLOUDINARY_API_KEY="${{ env.CLOUDINARY_API_KEY }}" \
          CLOUDINARY_API_SECRET="${{ env.CLOUDINARY_API_SECRET }}" \
          STRIPE_PUBLISHABLE_KEY="${{ env.STRIPE_PUBLISHABLE_KEY }}" \
          STRIPE_SECRET_KEY="${{ env.STRIPE_SECRET_KEY }}" \
          JWT_SECRET="${{ env.JWT_SECRET }}" \
          --app nafaplace-${{ matrix.webapp.name }} || echo "App may not exist yet"

    - name: Deploy ${{ matrix.webapp.name }} to Fly.io
      run: |
        cd ${{ matrix.webapp.path }}
        flyctl deploy --remote-only --ha=false

  notify-deployment:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-apis, deploy-web-apps]
    if: always()

    steps:
    - name: Deployment Success
      if: needs.deploy-apis.result == 'success' && needs.deploy-web-apps.result == 'success'
      run: |
        echo "✅ Test deployment successful!"
        echo ""
        echo "🌐 **Web Applications:**"
        echo "   • Main Web: https://nafaplace-web-test.fly.dev"
        echo "   • Admin Portal: https://nafaplace-admin-test.fly.dev"
        echo "   • Seller Portal: https://nafaplace-seller-test.fly.dev"
        echo ""
        echo "🔧 **API Services:**"
        echo "   • API Gateway: https://nafaplace-api-gateway.fly.dev"
        echo "   • Identity API: https://nafaplace-identity-api.fly.dev"
        echo "   • Catalog API: https://nafaplace-catalog-api.fly.dev"
        echo "   • Order API: https://nafaplace-order-api.fly.dev"
        echo "   • Cart API: https://nafaplace-cart-api.fly.dev"
        echo "   • Reviews API: https://nafaplace-reviews-api.fly.dev"
        echo "   • Notifications API: https://nafaplace-notifications-api.fly.dev"
        echo "   • Payment API: https://nafaplace-payment-api.fly.dev"

    - name: Deployment Failed
      if: needs.deploy-apis.result == 'failure' || needs.deploy-web-apps.result == 'failure'
      run: |
        echo "❌ Test deployment failed!"
        echo "Check the logs above for details."
        exit 1
