name: Deploy Backend API to Fly.io Test

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy-database:
    name: Deploy PostgreSQL Database
    runs-on: ubuntu-latest
    environment: test

    steps:
    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create PostgreSQL database if not exists
      run: |
        if ! flyctl apps list | grep -q "nafaplace-test-db"; then
          echo "Creating PostgreSQL database: nafaplace-test-db"
          flyctl postgres create --name nafaplace-test-db --region cdg --vm-size shared-cpu-1x --volume-size 3 --initial-cluster-size 1
        else
          echo "Database nafaplace-test-db already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  deploy-api:
    name: Deploy API Gateway
    runs-on: ubuntu-latest
    environment: test
    needs: deploy-database

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 9.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --no-restore --configuration Release

    - name: Run tests
      run: dotnet test --no-build --verbosity normal --configuration Release

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Create Fly.io app if not exists
      run: |
        if ! flyctl apps list | grep -q "nafaplace-test"; then
          echo "Creating new Fly.io app: nafaplace-test"
          flyctl apps create nafaplace-test --org personal
        else
          echo "App nafaplace-test already exists"
        fi
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Attach database to app
      run: |
        flyctl postgres attach --app nafaplace-test nafaplace-test-db || echo "Database already attached or attachment failed"
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Set Fly.io secrets
      run: |
        flyctl secrets set \
          CLOUDINARY_CLOUD_NAME="${{ secrets.CLOUDINARY_CLOUD_NAME }}" \
          CLOUDINARY_API_KEY="${{ secrets.CLOUDINARY_API_KEY }}" \
          CLOUDINARY_API_SECRET="${{ secrets.CLOUDINARY_API_SECRET }}" \
          STRIPE_PUBLISHABLE_KEY="${{ secrets.STRIPE_PUBLISHABLE_KEY }}" \
          STRIPE_SECRET_KEY="${{ secrets.STRIPE_SECRET_KEY }}" \
          JWT_SECRET="${{ secrets.JWT_SECRET }}" \
          ASPNETCORE_ENVIRONMENT="Staging"
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy to Fly.io Test
      run: flyctl deploy --remote-only
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Health check
      run: |
        echo "Waiting for deployment to be ready..."
        sleep 45
        curl -f https://nafaplace-test.fly.dev/ || echo "Health check failed, but deployment may still be starting"

    - name: Deployment notification
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo "✅ Déploiement de l'API Gateway réussi sur https://nafaplace-test.fly.dev"
          echo "🔗 Les portails peuvent maintenant se connecter aux APIs backend"
        else
          echo "❌ Échec du déploiement de l'API Gateway"
        fi
