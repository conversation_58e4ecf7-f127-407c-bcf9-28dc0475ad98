name: Deploy NafaPlace to Fly.io

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

jobs:
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 9.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --no-restore --configuration Release

  deploy-backend:
    name: Deploy Backend Services
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Set environment secrets
      run: |
        # Set secrets for all existing apps
        for app in nafaplace-identity-api nafaplace-catalog-api nafaplace-order-api nafaplace-cart-api nafaplace-reviews-api nafaplace-notifications-api nafaplace-payment-api nafaplace-api-gateway; do
          echo "Setting secrets for $app..."
          flyctl secrets set \
            CLOUDINARY_CLOUD_NAME="${{ secrets.CLOUDINARY_CLOUD_NAME }}" \
            CLOUDINARY_API_KEY="${{ secrets.CLOUDINARY_API_KEY }}" \
            CLOUDINARY_API_SECRET="${{ secrets.CLOUDINARY_API_SECRET }}" \
            STRIPE_PUBLISHABLE_KEY="${{ secrets.STRIPE_PUBLISHABLE_KEY }}" \
            STRIPE_SECRET_KEY="${{ secrets.STRIPE_SECRET_KEY }}" \
            JWT_SECRET="${{ secrets.JWT_SECRET }}" \
            ASPNETCORE_ENVIRONMENT="Production" \
            --app $app || echo "Failed to set secrets for $app"
        done

    - name: Deploy Identity API
      run: |
        cd src/Services/Identity/NafaPlace.Identity.API
        flyctl deploy --remote-only --app nafaplace-identity-api

    - name: Deploy Catalog API
      run: |
        cd src/Services/Catalog/NafaPlace.Catalog.API
        flyctl deploy --remote-only --app nafaplace-catalog-api

    - name: Deploy Order API
      run: |
        cd src/Services/Order/NafaPlace.Order.API
        flyctl deploy --remote-only --app nafaplace-order-api

    - name: Deploy Cart API
      run: |
        cd src/Services/Cart/NafaPlace.Cart.API
        flyctl deploy --remote-only --app nafaplace-cart-api

    - name: Deploy Reviews API
      run: |
        cd src/Services/Reviews/NafaPlace.Reviews.API
        flyctl deploy --remote-only --app nafaplace-reviews-api

    - name: Deploy Notifications API
      run: |
        cd src/Services/Notifications/NafaPlace.Notifications.API
        flyctl deploy --remote-only --app nafaplace-notifications-api

    - name: Deploy Payment API
      run: |
        cd src/Services/Payment/NafaPlace.Payment.API
        flyctl deploy --remote-only --app nafaplace-payment-api

    - name: Deploy API Gateway
      run: |
        cd src/ApiGateway/NafaPlace.ApiGateway
        flyctl deploy --remote-only --app nafaplace-api-gateway

  deploy-frontend:
    name: Deploy Frontend Applications
    runs-on: ubuntu-latest
    needs: deploy-backend
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Set secrets for web applications
      run: |
        # Set secrets for web apps
        for app in nafaplace-web-test nafaplace-admin-test nafaplace-seller-test; do
          echo "Setting secrets for $app..."
          flyctl secrets set \
            CLOUDINARY_CLOUD_NAME="${{ secrets.CLOUDINARY_CLOUD_NAME }}" \
            CLOUDINARY_API_KEY="${{ secrets.CLOUDINARY_API_KEY }}" \
            CLOUDINARY_API_SECRET="${{ secrets.CLOUDINARY_API_SECRET }}" \
            STRIPE_PUBLISHABLE_KEY="${{ secrets.STRIPE_PUBLISHABLE_KEY }}" \
            STRIPE_SECRET_KEY="${{ secrets.STRIPE_SECRET_KEY }}" \
            JWT_SECRET="${{ secrets.JWT_SECRET }}" \
            ASPNETCORE_ENVIRONMENT="Production" \
            --app $app || echo "Failed to set secrets for $app"
        done

    - name: Deploy Main Web Application
      run: |
        cd src/Web/NafaPlace.Web
        flyctl deploy --remote-only --app nafaplace-web-test

    - name: Deploy Admin Portal
      run: |
        cd src/Web/AdminPortal/NafaPlace.AdminPortal
        flyctl deploy --remote-only --app nafaplace-admin-test

    - name: Deploy Seller Portal
      run: |
        cd src/Web/SellerPortal/NafaPlace.SellerPortal
        flyctl deploy --remote-only --app nafaplace-seller-test

  notify-deployment:
    name: Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-backend, deploy-frontend]
    if: always()

    steps:
    - name: Deployment Success
      if: needs.deploy-backend.result == 'success' && needs.deploy-frontend.result == 'success'
      run: |
        echo "🎉 NafaPlace deployment successful!"
        echo ""
        echo "🌐 **Web Applications:**"
        echo "   • Main Web: https://nafaplace-web-test.fly.dev"
        echo "   • Admin Portal: https://nafaplace-admin-test.fly.dev"
        echo "   • Seller Portal: https://nafaplace-seller-test.fly.dev"
        echo ""
        echo "🔧 **Backend Services:**"
        echo "   • API Gateway: https://nafaplace-api-gateway.fly.dev"
        echo "   • Identity API: https://nafaplace-identity-api.fly.dev"
        echo "   • Catalog API: https://nafaplace-catalog-api.fly.dev"
        echo "   • Order API: https://nafaplace-order-api.fly.dev"
        echo "   • Cart API: https://nafaplace-cart-api.fly.dev"
        echo "   • Reviews API: https://nafaplace-reviews-api.fly.dev"
        echo "   • Notifications API: https://nafaplace-notifications-api.fly.dev"
        echo "   • Payment API: https://nafaplace-payment-api.fly.dev"
        echo ""
        echo "✅ All services are now live and ready for testing!"

    - name: Deployment Failed
      if: needs.deploy-backend.result == 'failure' || needs.deploy-frontend.result == 'failure'
      run: |
        echo "❌ NafaPlace deployment failed!"
        echo "Please check the logs above for details."
        exit 1
