using NafaPlace.Identity.Application.DTOs.Role;

namespace NafaPlace.Identity.Application.Common.Interfaces;

public interface IRoleService
{
    Task<RoleDto> CreateRoleAsync(CreateRoleRequest request);
    Task<RoleDto> UpdateRoleAsync(int id, UpdateRoleRequest request);
    Task DeleteRoleAsync(int id);
    Task<RoleDto> GetRoleByIdAsync(int id);
    Task<IEnumerable<RoleDto>> GetAllRolesAsync();
    Task AssignRoleToUserAsync(int userId, int roleId);
    Task RemoveRoleFromUserAsync(int userId, int roleId);
    Task<IEnumerable<RoleDto>> GetUserRolesAsync(int userId);
    Task UpdateUserRolesAsync(int userId, List<string> roleNames);
}
