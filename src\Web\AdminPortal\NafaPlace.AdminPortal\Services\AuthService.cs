using System.Net.Http.Json;
using System.Net.Http.Headers;
using Microsoft.AspNetCore.Components.Authorization;
using Blazored.LocalStorage;
using NafaPlace.AdminPortal.Models.Auth;
using NafaPlace.AdminPortal.Pages.Users;

namespace NafaPlace.AdminPortal.Services;

public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly AuthenticationStateProvider _authStateProvider;

    // Event to notify authentication state changes
    public event Action? AuthenticationStateChanged;

    public AuthService(HttpClient httpClient, ILocalStorageService localStorage, AuthenticationStateProvider authStateProvider)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _authStateProvider = authStateProvider;
    }

    public async Task<AuthResponse> LoginAsync(string email, string password)
    {
        try
        {
            Console.WriteLine($"Tentative de connexion avec l'utilisateur: {email}");
            
            // Créer une requête adaptée au format attendu par l'API
            var apiRequest = new
            {
                Username = email,
                Password = password
            };
            
            // Envoyer la requête au format attendu par l'API
            var response = await _httpClient.PostAsJsonAsync("auth/login", apiRequest);

            Console.WriteLine($"Réponse du serveur: {response.StatusCode}");
            
            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponse>();
                if (authResponse != null)
                {
                    Console.WriteLine("Connexion réussie, récupération du token");
                    
                    // Stocker le token dans le localStorage
                    await _localStorage.SetItemAsync("authToken", authResponse.AccessToken);
                    
                    // Utiliser le CustomAuthStateProvider pour marquer l'utilisateur comme authentifié
                    ((CustomAuthStateProvider)_authStateProvider).MarkUserAsAuthenticated(authResponse.AccessToken);
                    
                    // Notifier le changement d'état d'authentification
                    AuthenticationStateChanged?.Invoke();
                    
                    return authResponse;
                }
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Erreur de connexion: {errorContent}");
            
            return new AuthResponse 
            { 
                Success = false, 
                Message = "Échec de l'authentification. Veuillez vérifier vos identifiants." 
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception lors de la connexion: {ex.Message}");
            return new AuthResponse 
            { 
                Success = false, 
                Message = $"Une erreur s'est produite: {ex.Message}" 
            };
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            // Supprimer le token du localStorage
            await _localStorage.RemoveItemAsync("authToken");
            
            // Utiliser la méthode MarkUserAsLoggedOut du CustomAuthStateProvider
            ((CustomAuthStateProvider)_authStateProvider).MarkUserAsLoggedOut();
            
            // Notifier le changement d'état d'authentification
            AuthenticationStateChanged?.Invoke();
            
            Console.WriteLine("Déconnexion réussie");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la déconnexion: {ex.Message}");
        }
    }

    public async Task<UserDto> GetCurrentUserAsync()
    {
        try
        {
            // Récupérer le token depuis le localStorage
            var token = await _localStorage.GetItemAsync<string>("authToken");
            
            if (string.IsNullOrEmpty(token))
            {
                Console.WriteLine("Token vide, utilisateur non authentifié");
                return new UserDto { IsAuthenticated = false };
            }
            
            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            
            // Appeler l'API pour récupérer les informations de l'utilisateur
            var response = await _httpClient.GetAsync("users/profile");
            
            if (response.IsSuccessStatusCode)
            {
                var user = await response.Content.ReadFromJsonAsync<UserDto>();
                if (user != null)
                {
                    user.IsAuthenticated = true;
                    return user;
                }
            }
            
            return new UserDto { IsAuthenticated = false };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de l'utilisateur actuel: {ex.Message}");
            return new UserDto { IsAuthenticated = false };
        }
    }

    public async Task<List<RoleDto>> GetRolesAsync()
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            using var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri("http://localhost:5155/");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Appeler l'API pour récupérer tous les rôles
            var response = await httpClient.GetAsync("api/roles");
            response.EnsureSuccessStatusCode();

            var roles = await response.Content.ReadFromJsonAsync<List<RoleDto>>();
            return roles ?? new List<RoleDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de tous les rôles: {ex.Message}");
            throw;
        }
    }

    public async Task<RoleDto> CreateRoleAsync(RoleDto role)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            using var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri("http://localhost:5155/");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Créer la requête avec le bon format
            var request = new
            {
                Name = role.Name,
                Description = role.Description
            };

            // Appeler l'API pour créer un nouveau rôle
            var response = await httpClient.PostAsJsonAsync("api/roles", request);
            response.EnsureSuccessStatusCode();

            var createdRole = await response.Content.ReadFromJsonAsync<RoleDto>();
            return createdRole ?? throw new Exception("Réponse invalide du serveur");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la création du rôle: {ex.Message}");
            throw;
        }
    }

    public async Task<RoleDto> UpdateRoleAsync(RoleDto role)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            using var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri("http://localhost:5155/");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Créer la requête avec le bon format
            var request = new
            {
                Name = role.Name,
                Description = role.Description
            };

            // Appeler l'API pour mettre à jour un rôle
            var response = await httpClient.PutAsJsonAsync($"api/roles/{role.Id}", request);
            response.EnsureSuccessStatusCode();

            var updatedRole = await response.Content.ReadFromJsonAsync<RoleDto>();
            return updatedRole ?? throw new Exception("Réponse invalide du serveur");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du rôle: {ex.Message}");
            throw;
        }
    }

    public async Task<bool> DeleteRoleAsync(int roleId)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            using var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri("http://localhost:5155/");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Appeler l'API pour supprimer un rôle
            var response = await httpClient.DeleteAsync($"api/roles/{roleId}");
            response.EnsureSuccessStatusCode();

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression du rôle: {ex.Message}");
            throw;
        }
    }

    public async Task<bool> AssignRoleToUserAsync(int userId, string roleName)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                throw new UnauthorizedAccessException("Token d'authentification manquant");
            }

            using var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri("http://localhost:5155/");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Créer la requête pour assigner un rôle à un utilisateur
            var request = new
            {
                UserId = userId,
                RoleName = roleName
            };

            // Appeler l'API pour assigner un rôle à un utilisateur
            var response = await httpClient.PostAsJsonAsync("api/users/assign-role", request);
            response.EnsureSuccessStatusCode();

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'assignation du rôle à l'utilisateur: {ex.Message}");
            throw;
        }
    }

    // Méthodes pour la gestion des utilisateurs
    public async Task<UserListResponse> GetUsersAsync(string searchTerm = "", int page = 1, int pageSize = 10)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return new UserListResponse();
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Construire l'URL avec les paramètres de recherche et de pagination
            var url = $"users?page={page}&pageSize={pageSize}";
            if (!string.IsNullOrEmpty(searchTerm))
            {
                url += $"&searchTerm={Uri.EscapeDataString(searchTerm)}";
            }

            // Appeler l'API pour récupérer les utilisateurs
            var response = await _httpClient.GetAsync(url);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<UserListResponse>();
                return result ?? new UserListResponse();
            }

            return new UserListResponse();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des utilisateurs: {ex.Message}");
            return new UserListResponse();
        }
    }

    public async Task<UserDto> GetUserByIdAsync(int userId)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return new UserDto();
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Appeler l'API pour récupérer un utilisateur par son ID
            var response = await _httpClient.GetAsync($"users/{userId}");

            if (response.IsSuccessStatusCode)
            {
                var user = await response.Content.ReadFromJsonAsync<UserDto>();
                return user ?? new UserDto();
            }

            return new UserDto();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de l'utilisateur: {ex.Message}");
            return new UserDto();
        }
    }

    public async Task<bool> RegisterUserAsync(object userModel)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Cette méthode n'est plus utilisée - utiliser UserService à la place
            return false;

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'enregistrement de l'utilisateur: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> UpdateUserAsync(object userModel)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Cette méthode n'est plus utilisée - utiliser UserService à la place
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de l'utilisateur: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> DeleteUserAsync(int userId)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Appeler l'API pour supprimer un utilisateur
            var response = await _httpClient.DeleteAsync($"api/users/{userId}");

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de l'utilisateur: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> UpdateUserRolesAsync(int userId, List<string> roles)
    {
        try
        {
            // Vérifier si l'utilisateur est authentifié
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            // Configurer l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            // Créer la requête pour mettre à jour les rôles d'un utilisateur
            var request = new
            {
                UserId = userId,
                Roles = roles
            };

            // Appeler l'API pour mettre à jour les rôles d'un utilisateur
            var response = await _httpClient.PostAsJsonAsync("api/users/update-roles", request);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour des rôles de l'utilisateur: {ex.Message}");
            return false;
        }
    }
}
