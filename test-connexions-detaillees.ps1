# Test détaillé des connexions avec gestion d'erreurs
Write-Host "=== Test Détaillé des Connexions NafaPlace ===" -ForegroundColor Green

# Variables pour stocker les comptes créés
$createdUser = $null
$createdSeller = $null

# Test 1: Création et connexion utilisateur
Write-Host "`n--- Test Utilisateur Standard ---" -ForegroundColor Yellow
$randomId = Get-Random -Minimum 1000 -Maximum 9999
$userEmail = "testuser$<EMAIL>"
$userUsername = "testuser$randomId"

try {
    # Création utilisateur
    $userBody = @{
        email = $userEmail
        username = $userUsername
        password = "User123!"
        firstName = "Test"
        lastName = "User"
    } | ConvertTo-Json

    $userResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/register" -Method POST -Body $userBody -ContentType "application/json"
    Write-Host "✅ Utilisateur créé: $userUsername" -ForegroundColor Green
    $createdUser = @{ email = $userEmail; password = "User123!" }
    
    # Connexion utilisateur
    Start-Sleep -Seconds 1
    $loginBody = @{
        email = $userEmail
        password = "User123!"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "✅ Connexion utilisateur réussie!" -ForegroundColor Green
    Write-Host "Token: $($loginResponse.token.Substring(0, 30))..." -ForegroundColor Gray
    
    # Test profil utilisateur
    $headers = @{ "Authorization" = "Bearer $($loginResponse.token)" }
    $profileResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/users/profile" -Headers $headers
    Write-Host "✅ Profil récupéré: $($profileResponse.username)" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Erreur utilisateur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorContent = $reader.ReadToEnd()
            Write-Host "Détails: $errorContent" -ForegroundColor Red
        } catch {}
    }
}

# Test 2: Création et connexion vendeur
Write-Host "`n--- Test Vendeur ---" -ForegroundColor Yellow
$sellerEmail = "testseller$<EMAIL>"
$sellerUsername = "testseller$randomId"

try {
    # Création vendeur
    $sellerBody = @{
        email = $sellerEmail
        username = $sellerUsername
        password = "Seller123!"
        firstName = "Test"
        lastName = "Seller"
        companyName = "Test Company $randomId"
        phoneNumber = "+224123456789"
    } | ConvertTo-Json

    $sellerResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/register-seller" -Method POST -Body $sellerBody -ContentType "application/json"
    Write-Host "✅ Vendeur créé: $sellerUsername" -ForegroundColor Green
    $createdSeller = @{ email = $sellerEmail; password = "Seller123!" }
    
    # Connexion vendeur
    Start-Sleep -Seconds 1
    $loginBody = @{
        email = $sellerEmail
        password = "Seller123!"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "✅ Connexion vendeur réussie!" -ForegroundColor Green
    Write-Host "Token: $($loginResponse.token.Substring(0, 30))..." -ForegroundColor Gray
    
    # Test profil vendeur
    $headers = @{ "Authorization" = "Bearer $($loginResponse.token)" }
    $profileResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/users/profile" -Headers $headers
    Write-Host "✅ Profil vendeur récupéré: $($profileResponse.username)" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Erreur vendeur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorContent = $reader.ReadToEnd()
            Write-Host "Détails: $errorContent" -ForegroundColor Red
        } catch {}
    }
}

# Test 3: Test connexion admin avec différents formats
Write-Host "`n--- Test Admin (différents formats) ---" -ForegroundColor Yellow

# Format 1: avec email
try {
    $adminBody1 = @{
        email = "<EMAIL>"
        password = "Admin123!"
    } | ConvertTo-Json

    $adminResponse1 = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $adminBody1 -ContentType "application/json"
    Write-Host "✅ Connexion admin (email) réussie!" -ForegroundColor Green
    Write-Host "Token: $($adminResponse1.token.Substring(0, 30))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Échec admin (email): $($_.Exception.Message)" -ForegroundColor Red
}

# Format 2: avec username
try {
    $adminBody2 = @{
        username = "admin"
        password = "Admin123!"
    } | ConvertTo-Json

    $adminResponse2 = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $adminBody2 -ContentType "application/json"
    Write-Host "✅ Connexion admin (username) réussie!" -ForegroundColor Green
    Write-Host "Token: $($adminResponse2.token.Substring(0, 30))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Échec admin (username): $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Vérification des utilisateurs existants
Write-Host "`n--- Vérification Base de Données ---" -ForegroundColor Yellow
try {
    # Essayer de récupérer les utilisateurs sans authentification (pour debug)
    $usersResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/users" -Method GET
    Write-Host "✅ Utilisateurs dans la DB: $($usersResponse.Count)" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Impossible de récupérer les utilisateurs sans auth (normal)" -ForegroundColor Yellow
}

Write-Host "`n=== Résumé des Tests ===" -ForegroundColor Green
Write-Host "Comptes créés pour tests manuels:" -ForegroundColor White
if ($createdUser) {
    Write-Host "👤 Utilisateur: $($createdUser.email) / $($createdUser.password)" -ForegroundColor Cyan
}
if ($createdSeller) {
    Write-Host "🏪 Vendeur: $($createdSeller.email) / $($createdSeller.password)" -ForegroundColor Cyan
}
Write-Host "🔧 Admin: <EMAIL> / Admin123! (à vérifier)" -ForegroundColor Yellow

Write-Host "`nPortails web disponibles:" -ForegroundColor White
Write-Host "- Main Web: http://localhost:8080" -ForegroundColor Cyan
Write-Host "- Admin Portal: http://localhost:8081" -ForegroundColor Cyan  
Write-Host "- Seller Portal: http://localhost:8082" -ForegroundColor Cyan
