@page "/roles"
@using Microsoft.AspNetCore.Components.Authorization
@using NafaPlace.AdminPortal.Models.Auth
@using RoleModels = NafaPlace.AdminPortal.Models.Users
@using NafaPlace.AdminPortal.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Routing
@using System.ComponentModel.DataAnnotations
@inject IAuthService AuthService
@inject IUserService UserService
@inject NavigationManager NavigationManager
@attribute [Authorize(Roles = "Admin")]

<PageTitle>Gestion des Rôles - NafaPlace Admin</PageTitle>

<div class="container-fluid py-4">
    <h2 class="mb-4">Gestion des Rôles</h2>

    @if (isLoading)
    {
        <div class="d-flex justify-content-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else
    {
        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger" role="alert">
                @errorMessage
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success" role="alert">
                @successMessage
            </div>
        }

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Liste des Rôles</h5>
                    </div>
                    <div class="card-body">
                        @if (roles.Count == 0)
                        {
                            <p class="text-muted">Aucun rôle trouvé.</p>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Description</th>
                                            <th>Date de création</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var role in roles)
                                        {
                                            <tr>
                                                <td>@role.Name</td>
                                                <td>@role.Description</td>
                                                <td>@role.CreatedAt.ToShortDateString()</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary me-1" @onclick="() => EditRole(role)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" @onclick="() => DeleteRoleConfirmation(role)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">@(isEditing ? "Modifier le Rôle" : "Créer un Nouveau Rôle")</h5>
                    </div>
                    <div class="card-body">
                        @if (isEditing)
                        {
                            <EditForm Model="@updateRoleRequest" OnValidSubmit="SaveRole">
                                <DataAnnotationsValidator />
                                <ValidationSummary />

                                <div class="mb-3">
                                    <label for="roleName" class="form-label">Nom du Rôle</label>
                                    <InputText id="roleName" @bind-Value="updateRoleRequest.Name" class="form-control" />
                                    <ValidationMessage For="@(() => updateRoleRequest.Name)" />
                                </div>

                                <div class="mb-3">
                                    <label for="roleDescription" class="form-label">Description</label>
                                    <InputTextArea id="roleDescription" @bind-Value="updateRoleRequest.Description" class="form-control" rows="3" />
                                    <ValidationMessage For="@(() => updateRoleRequest.Description)" />
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                        @if (isLoading)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                        }
                                        Mettre à jour
                                    </button>
                                    <button type="button" class="btn btn-secondary" @onclick="CancelEdit">
                                        Annuler
                                    </button>
                                </div>
                            </EditForm>
                        }
                        else
                        {
                            <EditForm Model="@createRoleRequest" OnValidSubmit="SaveRole">
                                <DataAnnotationsValidator />
                                <ValidationSummary />

                                <div class="mb-3">
                                    <label for="roleName" class="form-label">Nom du Rôle</label>
                                    <InputText id="roleName" @bind-Value="createRoleRequest.Name" class="form-control" />
                                    <ValidationMessage For="@(() => createRoleRequest.Name)" />
                                </div>

                                <div class="mb-3">
                                    <label for="roleDescription" class="form-label">Description</label>
                                    <InputTextArea id="roleDescription" @bind-Value="createRoleRequest.Description" class="form-control" rows="3" />
                                    <ValidationMessage For="@(() => createRoleRequest.Description)" />
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                        @if (isLoading)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                        }
                                        Créer
                                    </button>
                                </div>
                            </EditForm>
                        }


                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Assigner un Rôle à un Utilisateur</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="userId" class="form-label">ID de l'Utilisateur</label>
                            <input type="number" id="userId" class="form-control" @bind="userId" placeholder="ID de l'utilisateur" />
                        </div>

                        <div class="mb-3">
                            <label for="roleSelect" class="form-label">Rôle à Assigner</label>
                            <select id="roleSelect" class="form-select" @bind="selectedRoleName">
                                <option value="">Sélectionnez un rôle</option>
                                @foreach (var role in roles)
                                {
                                    <option value="@role.Name">@role.Name</option>
                                }
                            </select>
                        </div>

                        <button type="button" class="btn btn-success" @onclick="AssignRoleToUser" disabled="@(userId <= 0 || string.IsNullOrEmpty(selectedRoleName))">
                            Assigner le Rôle
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (showDeleteConfirmation)
    {
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirmation de suppression</h5>
                        <button type="button" class="btn-close" @onclick="() => showDeleteConfirmation = false"></button>
                    </div>
                    <div class="modal-body">
                        <p>Êtes-vous sûr de vouloir supprimer le rôle "@roleToDelete?.Name" ?</p>
                        <p class="text-danger">Cette action est irréversible.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="() => showDeleteConfirmation = false">Annuler</button>
                        <button type="button" class="btn btn-danger" @onclick="DeleteRole">Supprimer</button>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<RoleModels.RoleDto> roles = new List<RoleModels.RoleDto>();
    private RoleModels.RoleDto currentRole = new RoleModels.RoleDto();
    private RoleModels.CreateRoleRequest createRoleRequest = new RoleModels.CreateRoleRequest();
    private RoleModels.UpdateRoleRequest updateRoleRequest = new RoleModels.UpdateRoleRequest();
    private bool isLoading = true;
    private bool isEditing = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool showDeleteConfirmation = false;
    private RoleModels.RoleDto? roleToDelete;
    private int userId;
    private string selectedRoleName = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadRoles();
    }

    private async Task LoadRoles()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            
            roles = await UserService.GetRolesAsync();
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors du chargement des rôles: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private void EditRole(RoleModels.RoleDto role)
    {
        currentRole = new RoleModels.RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description,
            CreatedAt = role.CreatedAt,
            UpdatedAt = role.UpdatedAt
        };
        updateRoleRequest = new RoleModels.UpdateRoleRequest
        {
            Name = role.Name,
            Description = role.Description
        };
        isEditing = true;
    }

    private void CancelEdit()
    {
        currentRole = new RoleModels.RoleDto();
        createRoleRequest = new RoleModels.CreateRoleRequest();
        updateRoleRequest = new RoleModels.UpdateRoleRequest();
        isEditing = false;
    }

    private async Task SaveRole()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            if (isEditing)
            {
                var updatedRole = await UserService.UpdateRoleAsync(currentRole.Id, updateRoleRequest);
                successMessage = "Rôle mis à jour avec succès.";
            }
            else
            {
                var createdRole = await UserService.CreateRoleAsync(createRoleRequest);
                successMessage = "Rôle créé avec succès.";
            }
            
            await LoadRoles();
            currentRole = new RoleModels.RoleDto();
            createRoleRequest = new RoleModels.CreateRoleRequest();
            updateRoleRequest = new RoleModels.UpdateRoleRequest();
            isEditing = false;
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de l'enregistrement du rôle: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private void DeleteRoleConfirmation(RoleModels.RoleDto role)
    {
        roleToDelete = role;
        showDeleteConfirmation = true;
    }

    private async Task DeleteRole()
    {
        try
        {
            if (roleToDelete != null)
            {
                isLoading = true;
                errorMessage = string.Empty;
                
                await UserService.DeleteRoleAsync(roleToDelete.Id);
                successMessage = "Rôle supprimé avec succès.";
                
                await LoadRoles();
                showDeleteConfirmation = false;
                roleToDelete = null;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de la suppression du rôle: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task AssignRoleToUser()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            var result = await AuthService.AssignRoleToUserAsync(userId, selectedRoleName);
            if (result)
            {
                successMessage = "Rôle assigné à l'utilisateur avec succès.";
                userId = 0;
                selectedRoleName = string.Empty;
            }
            else
            {
                errorMessage = "Erreur lors de l'assignation du rôle à l'utilisateur.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors de l'assignation du rôle à l'utilisateur: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }
}
