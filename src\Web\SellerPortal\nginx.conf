events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Types MIME pour Blazor WebAssembly
    types {
        application/wasm wasm;
    }

    # Compression - exclure les fichiers WASM pour éviter les erreurs SRI
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;

        # Health check endpoint
        location = /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html =404;

            # CORS headers
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Range, If-Range" always;
        }

        # Configuration spécifique pour les fichiers WASM - pas de compression
        location ~* \.wasm$ {
            add_header Content-Type application/wasm;
            add_header Access-Control-Allow-Origin "*" always;
            add_header Cache-Control "public, max-age=31536000, immutable";
            gzip off;
            try_files $uri =404;
        }

        # Configuration pour les fichiers du framework Blazor
        location /_framework/ {
            root /usr/share/nginx/html;
            add_header Cache-Control "public, max-age=31536000, immutable";
            add_header Access-Control-Allow-Origin "*" always;
            # Désactiver la compression pour tous les fichiers du framework
            gzip off;
            try_files $uri =404;
        }
    }
}
