# Script pour corriger tous les endpoints selon le commit de référence bfdb2d96dcc5c2ef2789d1ebe9050d103b1f8864

Write-Host "=== CORRECTION DE TOUS LES ENDPOINTS SELON LE COMMIT DE RÉFÉRENCE ===" -ForegroundColor Green

# Fonction pour corriger les endpoints dans un fichier
function Fix-Endpoints {
    param(
        [string]$FilePath,
        [string]$ServiceName
    )
    
    if (Test-Path $FilePath) {
        Write-Host "Correction des endpoints dans $ServiceName..." -ForegroundColor Yellow
        
        # Lire le contenu du fichier
        $content = Get-Content $FilePath -Raw
        
        # Corrections spécifiques basées sur le commit de référence
        $corrections = @{
            # Catalog API endpoints - ajouter /api/ au début
            '"v1/products' = '"/api/v1/products'
            '"v1/categories' = '"/api/v1/categories'
            '"v1/sellers' = '"/api/v1/sellers'
            
            # Auth API endpoints - <PERSON><PERSON><PERSON><PERSON> corrects avec api/auth/
            # Identity API endpoints - <PERSON><PERSON><PERSON><PERSON> corrects avec api/users/
            
            # Order API endpoints
            '"orders/' = '"/api/orders/'
            '"orders"' = '"/api/orders"'
            
            # Cart API endpoints
            '"cart/' = '"/api/cart/'
            '"cart"' = '"/api/cart"'
            
            # Payment API endpoints
            '"payments/' = '"/api/payments/'
            '"payments"' = '"/api/payments"'
            
            # Review API endpoints
            '"reviews/' = '"/api/reviews/'
            '"reviews"' = '"/api/reviews"'
        }
        
        $modified = $false
        foreach ($old in $corrections.Keys) {
            $new = $corrections[$old]
            if ($content -match [regex]::Escape($old)) {
                $content = $content -replace [regex]::Escape($old), $new
                $modified = $true
                Write-Host "  ✅ Corrigé: $old -> $new" -ForegroundColor Green
            }
        }
        
        if ($modified) {
            Set-Content -Path $FilePath -Value $content -Encoding UTF8
            Write-Host "  📝 Fichier $ServiceName mis à jour" -ForegroundColor Green
        } else {
            Write-Host "  ℹ️ Aucune correction nécessaire pour $ServiceName" -ForegroundColor Cyan
        }
    } else {
        Write-Host "  ❌ Fichier non trouvé: $FilePath" -ForegroundColor Red
    }
}

# Corriger tous les services dans Admin Portal
Write-Host "`n--- ADMIN PORTAL ---" -ForegroundColor Magenta
Fix-Endpoints "src/Web/AdminPortal/NafaPlace.AdminPortal/Services/ProductService.cs" "Admin ProductService"
Fix-Endpoints "src/Web/AdminPortal/NafaPlace.AdminPortal/Services/CategoryService.cs" "Admin CategoryService"
Fix-Endpoints "src/Web/AdminPortal/NafaPlace.AdminPortal/Services/OrderService.cs" "Admin OrderService"
Fix-Endpoints "src/Web/AdminPortal/NafaPlace.AdminPortal/Services/UserService.cs" "Admin UserService"
Fix-Endpoints "src/Web/AdminPortal/NafaPlace.AdminPortal/Services/ReviewService.cs" "Admin ReviewService"

# Corriger tous les services dans Seller Portal
Write-Host "`n--- SELLER PORTAL ---" -ForegroundColor Magenta
Fix-Endpoints "src/Web/SellerPortal/NafaPlace.SellerPortal/Services/ProductService.cs" "Seller ProductService"
Fix-Endpoints "src/Web/SellerPortal/NafaPlace.SellerPortal/Services/OrderService.cs" "Seller OrderService"
Fix-Endpoints "src/Web/SellerPortal/NafaPlace.SellerPortal/Services/ReviewService.cs" "Seller ReviewService"
Fix-Endpoints "src/Web/SellerPortal/NafaPlace.SellerPortal/Services/StatisticsService.cs" "Seller StatisticsService"

# Corriger tous les services dans Main Web Portal
Write-Host "`n--- MAIN WEB PORTAL ---" -ForegroundColor Magenta
Fix-Endpoints "src/Web/NafaPlace.Web/Services/ProductService.cs" "Web ProductService"
Fix-Endpoints "src/Web/NafaPlace.Web/Services/CategoryService.cs" "Web CategoryService"
Fix-Endpoints "src/Web/NafaPlace.Web/Services/CartService.cs" "Web CartService"
Fix-Endpoints "src/Web/NafaPlace.Web/Services/OrderService.cs" "Web OrderService"
Fix-Endpoints "src/Web/NafaPlace.Web/Services/PaymentService.cs" "Web PaymentService"
Fix-Endpoints "src/Web/NafaPlace.Web/Services/ReviewService.cs" "Web ReviewService"

Write-Host "`n=== CORRECTION TERMINÉE ===" -ForegroundColor Green
Write-Host "Tous les endpoints ont été corrigés selon le commit de référence." -ForegroundColor Green
Write-Host "Prochaine étape: Reconstruire les conteneurs Docker" -ForegroundColor Yellow
