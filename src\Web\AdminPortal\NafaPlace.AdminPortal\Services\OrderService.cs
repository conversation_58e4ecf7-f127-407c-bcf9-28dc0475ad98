using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.AdminPortal.Models.Orders;
using NafaPlace.AdminPortal.Models.Common;
using NafaPlace.AdminPortal.Models.Users;
using AuthUserDto = NafaPlace.AdminPortal.Models.Auth.UserDto;
using Blazored.LocalStorage;
using System.Net.Http.Headers;

namespace NafaPlace.AdminPortal.Services;

public interface IOrderService
{
    Task<PagedResult<OrderDto>> GetOrdersAsync(OrderSearchRequest request);
    Task<OrderDto?> GetOrderByIdAsync(string orderNumber);
    Task<OrderDto> UpdateOrderStatusAsync(string orderNumber, UpdateOrderStatusRequest request);
    Task<OrderStatistics> GetOrderStatisticsAsync();
}

public class OrderService : IOrderService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly IAuthService _authService;
    private readonly ILocalStorageService _localStorage;

    public OrderService(HttpClient httpClient, IAuthService authService, ILocalStorageService localStorage)
    {
        _httpClient = httpClient;
        _authService = authService;
        _localStorage = localStorage;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<PagedResult<OrderDto>> GetOrdersAsync(OrderSearchRequest request)
    {
        try
        {
            var queryParams = new List<string>
            {
                $"pageNumber={request.PageNumber}",
                $"pageSize={request.PageSize}"
            };

            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");

            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={Uri.EscapeDataString(request.Status)}");

            if (!string.IsNullOrEmpty(request.PaymentStatus))
                queryParams.Add($"paymentStatus={Uri.EscapeDataString(request.PaymentStatus)}");

            if (request.StartDate.HasValue)
                queryParams.Add($"startDate={request.StartDate.Value:yyyy-MM-dd}");

            if (request.EndDate.HasValue)
                queryParams.Add($"endDate={request.EndDate.Value:yyyy-MM-dd}");

            var query = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"?{query}");
            response.EnsureSuccessStatusCode();

            // Lire les données de l'API Order
            var apiOrders = await response.Content.ReadFromJsonAsync<List<OrderApiDto>>(_jsonOptions);

            if (apiOrders != null)
            {
                // Convertir les données de l'API vers nos DTOs avec enrichissement des données utilisateur
                var orderDtos = new List<OrderDto>();

                foreach (var o in apiOrders)
                {
                    var userInfo = await GetUserInfoAsync(o.UserId);

                    orderDtos.Add(new OrderDto
                    {
                        Id = 0, // Pas d'ID int dans l'API, on utilise 0 pour le moment
                        OrderNumber = o.Id.ToString(), // Utiliser l'ID comme numéro de commande
                        UserId = userInfo.Id,
                        UserName = userInfo.FullName,
                        UserEmail = userInfo.Email,
                    OrderDate = o.OrderDate,
                    TotalAmount = o.TotalAmount,
                    Currency = o.Currency,
                    Status = o.Status,
                    PaymentStatus = o.PaymentStatus,
                    PaymentMethod = o.PaymentMethod,
                    ShippingAddress = o.ShippingAddress != null ? new ShippingAddressDto
                    {
                        FirstName = o.ShippingAddress.FullName.Split(' ').FirstOrDefault() ?? "",
                        LastName = o.ShippingAddress.FullName.Split(' ').Skip(1).FirstOrDefault() ?? "",
                        AddressLine1 = o.ShippingAddress.Address,
                        City = o.ShippingAddress.City,
                        Country = o.ShippingAddress.Country,
                        PostalCode = o.ShippingAddress.PostalCode,
                        PhoneNumber = o.ShippingAddress.PhoneNumber ?? ""
                    } : null,
                        Items = o.OrderItems?.Select(item => new OrderItemDto
                        {
                            Id = 0, // Pas d'ID int dans l'API
                            ProductId = item.ProductId,
                            ProductName = item.ProductName,
                            UnitPrice = item.UnitPrice,
                            Quantity = item.Quantity,
                            TotalPrice = item.UnitPrice * item.Quantity,
                            ProductImage = item.ImageUrl ?? string.Empty
                        }).ToList() ?? new List<OrderItemDto>()
                    });
                }

                // Appliquer la pagination
                var totalCount = orderDtos.Count;
                var pagedItems = orderDtos
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                return new PagedResult<OrderDto>
                {
                    Items = pagedItems,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }

            return new PagedResult<OrderDto>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des commandes: {ex.Message}");
            return new PagedResult<OrderDto>();
        }
    }

    public async Task<OrderDto?> GetOrderByIdAsync(string orderNumber)
    {
        try
        {
            // Convertir le numéro de commande en int
            if (int.TryParse(orderNumber, out var orderId))
            {
                var response = await _httpClient.GetAsync($"{orderId}");
                if (response.IsSuccessStatusCode)
                {
                    var apiOrder = await response.Content.ReadFromJsonAsync<OrderApiDto>(_jsonOptions);
                    if (apiOrder != null)
                    {
                        var userInfo = await GetUserInfoAsync(apiOrder.UserId);

                        return new OrderDto
                        {
                            Id = 0,
                            OrderNumber = apiOrder.Id.ToString(),
                            UserId = userInfo.Id,
                            UserName = userInfo.FullName,
                            UserEmail = userInfo.Email,
                            OrderDate = apiOrder.OrderDate,
                            TotalAmount = apiOrder.TotalAmount,
                            Currency = apiOrder.Currency,
                            Status = apiOrder.Status,
                            PaymentStatus = apiOrder.PaymentStatus,
                            PaymentMethod = apiOrder.PaymentMethod,
                            ShippingAddress = apiOrder.ShippingAddress != null ? new ShippingAddressDto
                            {
                                FirstName = apiOrder.ShippingAddress.FullName.Split(' ').FirstOrDefault() ?? "",
                                LastName = apiOrder.ShippingAddress.FullName.Split(' ').Skip(1).FirstOrDefault() ?? "",
                                AddressLine1 = apiOrder.ShippingAddress.Address,
                                City = apiOrder.ShippingAddress.City,
                                Country = apiOrder.ShippingAddress.Country,
                                PostalCode = apiOrder.ShippingAddress.PostalCode,
                                PhoneNumber = apiOrder.ShippingAddress.PhoneNumber ?? ""
                            } : null,
                            Items = apiOrder.OrderItems?.Select(item => new OrderItemDto
                            {
                                Id = 0,
                                ProductId = item.ProductId,
                                ProductName = item.ProductName,
                                UnitPrice = item.UnitPrice,
                                Quantity = item.Quantity,
                                TotalPrice = item.UnitPrice * item.Quantity,
                                ProductImage = item.ImageUrl ?? string.Empty
                            }).ToList() ?? new List<OrderItemDto>()
                        };
                    }
                }
            }
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de la commande {orderNumber}: {ex.Message}");
            return null;
        }
    }

    public async Task<OrderDto> UpdateOrderStatusAsync(string orderNumber, UpdateOrderStatusRequest request)
    {
        try
        {
            // Convertir le numéro de commande en int
            if (int.TryParse(orderNumber, out var orderId))
            {
                var response = await _httpClient.PutAsJsonAsync($"orders/{orderId}/status", request, _jsonOptions);
                response.EnsureSuccessStatusCode();

                var result = await response.Content.ReadFromJsonAsync<OrderDto>(_jsonOptions);
                return result ?? throw new Exception("Réponse invalide du serveur");
            }
            throw new Exception("Numéro de commande invalide");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du statut de la commande {orderNumber}: {ex.Message}");
            throw;
        }
    }

    public async Task<OrderStatistics> GetOrderStatisticsAsync()
    {
        try
        {
            // Récupérer toutes les commandes pour calculer les statistiques
            var response = await _httpClient.GetAsync("");

            if (response.IsSuccessStatusCode)
            {
                var apiOrders = await response.Content.ReadFromJsonAsync<List<OrderApiDto>>(_jsonOptions);

                if (apiOrders != null)
                {
                    var totalOrders = apiOrders.Count;
                    var pendingOrders = apiOrders.Count(o => o.Status == "Pending");
                    var processingOrders = apiOrders.Count(o => o.Status == "Processing");
                    var shippedOrders = apiOrders.Count(o => o.Status == "Shipped");
                    var deliveredOrders = apiOrders.Count(o => o.Status == "Delivered");
                    var cancelledOrders = apiOrders.Count(o => o.Status == "Cancelled");
                    var totalRevenue = apiOrders.Where(o => o.PaymentStatus == "Completed" || o.PaymentStatus == "Paid").Sum(o => o.TotalAmount);
                    var monthlyRevenue = apiOrders.Where(o => (o.PaymentStatus == "Completed" || o.PaymentStatus == "Paid") && o.OrderDate >= DateTime.Now.AddMonths(-1)).Sum(o => o.TotalAmount);
                    var averageOrderValue = totalOrders > 0 ? apiOrders.Average(o => o.TotalAmount) : 0;

                    return new OrderStatistics
                    {
                        TotalOrders = totalOrders,
                        PendingOrders = pendingOrders,
                        ProcessingOrders = processingOrders,
                        ShippedOrders = shippedOrders,
                        DeliveredOrders = deliveredOrders,
                        CancelledOrders = cancelledOrders,
                        TotalRevenue = totalRevenue,
                        MonthlyRevenue = monthlyRevenue,
                        AverageOrderValue = averageOrderValue
                    };
                }
            }

            return new OrderStatistics();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des statistiques: {ex.Message}");
            return new OrderStatistics();
        }
    }

    private async Task<UserDto> GetUserInfoAsync(string userId)
    {
        try
        {
            // Essayer de convertir l'userId string en int pour l'API Identity
            if (int.TryParse(userId, out var userIdInt))
            {
                // Récupérer le token d'authentification
                var token = await _localStorage.GetItemAsync<string>("authToken");
                if (!string.IsNullOrEmpty(token))
                {
                    // Configurer l'en-tête d'autorisation pour l'appel direct à l'API Identity
                    // Utiliser l'AuthService pour récupérer les informations utilisateur
                    var authUser = await _authService.GetUserByIdAsync(userIdInt);
                    if (authUser != null && !string.IsNullOrEmpty(authUser.Email))
                    {
                        // Convertir AuthUserDto vers UserDto (Models.Users)
                        return new UserDto
                        {
                            Id = authUser.Id,
                            Email = authUser.Email,
                            FirstName = authUser.FirstName,
                            LastName = authUser.LastName,
                            PhoneNumber = authUser.PhoneNumber,
                            IsActive = authUser.IsActive,
                            IsEmailConfirmed = authUser.EmailConfirmed,
                            Roles = authUser.Roles,
                            CreatedAt = DateTime.UtcNow, // Valeur par défaut
                            LastLoginAt = null
                        };
                    }
                    else
                    {
                        Console.WriteLine($"Erreur lors de la récupération des informations utilisateur pour userId {userIdInt}");
                    }
                }
                else
                {
                    Console.WriteLine("Token d'authentification non trouvé");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des informations utilisateur pour {userId}: {ex.Message}");
        }

        // Retourner des informations par défaut si la récupération échoue
        return new UserDto
        {
            Id = int.TryParse(userId, out var id) ? id : 0,
            Email = $"user{userId}@nafaplace.com",
            FirstName = "Utilisateur",
            LastName = userId,
            PhoneNumber = "",
            IsActive = true,
            IsEmailConfirmed = false,
            Roles = new List<string>()
        };
    }
}
