Write-Host "=== Test d'accès aux APIs depuis l'hôte ===" -ForegroundColor Cyan

# Test Identity API
Write-Host "Test Identity API (port 5155)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body '{"email":"<EMAIL>","username":"","password":"Admin123!"}' -TimeoutSec 10
    Write-Host "SUCCESS: Identity API accessible depuis l'hôte!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Identity API inaccessible depuis l'hôte" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Catalog API
Write-Host "Test Catalog API (port 5243)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5243/api/categories" -Method GET -TimeoutSec 10
    Write-Host "SUCCESS: Catalog API accessible depuis l'hôte!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Catalog API inaccessible depuis l'hôte" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Cart API
Write-Host "Test Cart API (port 5003)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5003/health" -Method GET -TimeoutSec 10 -ErrorAction SilentlyContinue
    if ($response) {
        Write-Host "SUCCESS: Cart API accessible depuis l'hôte!" -ForegroundColor Green
        Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    } else {
        # Essayer un autre endpoint
        $response = Invoke-WebRequest -Uri "http://localhost:5003/api/cart" -Method GET -TimeoutSec 10
        Write-Host "SUCCESS: Cart API accessible depuis l'hôte!" -ForegroundColor Green
        Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    }
} catch {
    Write-Host "FAILED: Cart API inaccessible depuis l'hôte" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== Test terminé ===" -ForegroundColor Cyan
