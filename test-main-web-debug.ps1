# Test de diagnostic pour le Main Web Portal
Write-Host "=== Diagnostic Main Web Portal Authentication ===" -ForegroundColor Green

# Test 1: Format exact utilisé par le Main Web Portal
Write-Host "`nTest 1: Format Main Web Portal (Email + Username vide)" -ForegroundColor Yellow
try {
    $headers = @{
        "Content-Type" = "application/json"
        "Origin" = "http://localhost:8080"
        "Referer" = "http://localhost:8080/"
        "User-Agent" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    $body = @{
        Email = "<EMAIL>"
        Username = ""
        Password = "TestUser123!"
    } | ConvertTo-Json
    
    Write-Host "Body envoyé: $body" -ForegroundColor Cyan
    
    $response = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $body -Headers $headers
    Write-Host "✅ SUCCESS avec TestUser123!: Token $($response.token.Length) chars" -ForegroundColor Green
} catch {
    Write-Host "❌ ERREUR avec TestUser123!: $($_.Exception.Message)" -ForegroundColor Red
    
    # Test avec User123!
    try {
        $body2 = @{
            Email = "<EMAIL>"
            Username = ""
            Password = "User123!"
        } | ConvertTo-Json
        
        Write-Host "Tentative avec User123!..." -ForegroundColor Yellow
        Write-Host "Body envoyé: $body2" -ForegroundColor Cyan
        
        $response2 = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $body2 -Headers $headers
        Write-Host "✅ SUCCESS avec User123!: Token $($response2.token.Length) chars" -ForegroundColor Green
    } catch {
        Write-Host "❌ ERREUR avec User123! aussi: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 2: Vérifier les utilisateurs en base
Write-Host "`nTest 2: Vérification des utilisateurs en base" -ForegroundColor Yellow
try {
    # Test avec admin pour vérifier que l'API fonctionne
    $adminBody = @{
        Email = "<EMAIL>"
        Username = ""
        Password = "Admin123!"
    } | ConvertTo-Json
    
    $adminResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $adminBody -Headers $headers
    Write-Host "✅ Admin login OK: Token $($adminResponse.token.Length) chars" -ForegroundColor Green
    
    # Utiliser le token admin pour récupérer la liste des utilisateurs
    $authHeaders = @{
        "Authorization" = "Bearer $($adminResponse.token)"
        "Content-Type" = "application/json"
    }
    
    Write-Host "Tentative de récupération des utilisateurs..." -ForegroundColor Cyan
    try {
        $usersResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/users" -Method GET -Headers $authHeaders
        Write-Host "✅ Utilisateurs récupérés: $($usersResponse.Count) utilisateurs" -ForegroundColor Green
        
        # Chercher l'utilisateur testuser1732
        $testUser = $usersResponse | Where-Object { $_.Email -eq "<EMAIL>" }
        if ($testUser) {
            Write-Host "✅ Utilisateur <EMAIL> trouvé:" -ForegroundColor Green
            Write-Host "   - ID: $($testUser.Id)" -ForegroundColor White
            Write-Host "   - Username: $($testUser.Username)" -ForegroundColor White
            Write-Host "   - Email: $($testUser.Email)" -ForegroundColor White
            Write-Host "   - Roles: $($testUser.Roles -join ', ')" -ForegroundColor White
        } else {
            Write-Host "❌ Utilisateur <EMAIL> NON TROUVÉ" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Impossible de récupérer les utilisateurs: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Impossible de se connecter en tant qu'admin: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Format simplifié (seulement Email et Password)
Write-Host "`nTest 3: Format simplifié (Email + Password seulement)" -ForegroundColor Yellow
try {
    $simpleBody = @{
        Email = "<EMAIL>"
        Password = "User123!"
    } | ConvertTo-Json
    
    Write-Host "Body simplifié: $simpleBody" -ForegroundColor Cyan
    
    $simpleResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $simpleBody -Headers $headers
    Write-Host "✅ SUCCESS format simplifié: Token $($simpleResponse.token.Length) chars" -ForegroundColor Green
} catch {
    Write-Host "❌ ERREUR format simplifié: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Résumé du diagnostic ===" -ForegroundColor Green
Write-Host "Le problème semble être lié au mot de passe de l'utilisateur <EMAIL>" -ForegroundColor Yellow
Write-Host "Le bon mot de passe semble être 'User123!' et non 'TestUser123!'" -ForegroundColor Yellow
