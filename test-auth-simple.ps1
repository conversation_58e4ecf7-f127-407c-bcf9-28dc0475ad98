Write-Host "=== Test Authentication Simple ===" -ForegroundColor Cyan

# Test Identity API directement
Write-Host "Test Identity API..." -ForegroundColor Yellow

$body = @{
    email = "<EMAIL>"
    username = ""
    password = "Admin123!"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body $body
    Write-Host "SUCCESS: Identity API fonctionne!" -ForegroundColor Green
    Write-Host "Token recu: $($response.token.Length) caracteres" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Identity API ne fonctionne pas" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# Test API Gateway
Write-Host "Test API Gateway..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method POST -ContentType "application/json" -Body $body
    Write-Host "SUCCESS: API Gateway fonctionne!" -ForegroundColor Green
    Write-Host "Token recu: $($response.token.Length) caracteres" -ForegroundColor Green
} catch {
    Write-Host "FAILED: API Gateway ne fonctionne pas" -ForegroundColor Red
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "=== Test termine ===" -ForegroundColor Cyan
