# Test des endpoints corrigés
Write-Host "=== TEST DES ENDPOINTS CORRIGÉS ===" -ForegroundColor Green

# Fonction pour tester la connexion et récupérer le profil
function Test-LoginAndProfile {
    param($email, $password, $portalName)
    
    Write-Host "`n--- Test $portalName ---" -ForegroundColor Yellow
    
    try {
        # Test de connexion
        $loginData = @{
            Username = $email
            Password = $password
        }

        $loginResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" `
            -Method POST `
            -ContentType "application/json" `
            -Body ($loginData | ConvertTo-Json) `
            -ErrorAction Stop

        Write-Host "✅ $portalName - Connexion réussie" -ForegroundColor Green
        
        # Test d'accès au profil avec le bon endpoint
        $headers = @{
            "Authorization" = "Bearer $($loginResponse.accessToken)"
        }

        if ($portalName -eq "Admin Portal") {
            # Admin Portal utilise api/users/profile
            $profileResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/users/profile" `
                -Method GET `
                -Headers $headers `
                -ErrorAction Stop
        } else {
            # Seller Portal utilise api/auth/current-user
            $profileResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/current-user" `
                -Method GET `
                -Headers $headers `
                -ErrorAction Stop
        }

        Write-Host "✅ $portalName - Accès au profil réussi" -ForegroundColor Green
        Write-Host "   Utilisateur: $($profileResponse.firstName) $($profileResponse.lastName)" -ForegroundColor Cyan
        
        return $true
    } catch {
        Write-Host "❌ $portalName - Erreur" -ForegroundColor Red
        Write-Host "   $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Tests
$adminSuccess = Test-LoginAndProfile -email "<EMAIL>" -password "Admin123!" -portalName "Admin Portal"
$sellerSuccess = Test-LoginAndProfile -email "<EMAIL>" -password "Seller123!" -portalName "Seller Portal"
$userSuccess = Test-LoginAndProfile -email "<EMAIL>" -password "User123!" -portalName "Main Web Portal"

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Green
if ($adminSuccess) { Write-Host "✅ Admin Portal: OK" -ForegroundColor Green } else { Write-Host "❌ Admin Portal: ÉCHEC" -ForegroundColor Red }
if ($sellerSuccess) { Write-Host "✅ Seller Portal: OK" -ForegroundColor Green } else { Write-Host "❌ Seller Portal: ÉCHEC" -ForegroundColor Red }
if ($userSuccess) { Write-Host "✅ Main Web Portal: OK" -ForegroundColor Green } else { Write-Host "❌ Main Web Portal: ÉCHEC" -ForegroundColor Red }

if ($adminSuccess -and $sellerSuccess -and $userSuccess) {
    Write-Host "`n🎉 TOUS LES PORTAILS FONCTIONNENT CORRECTEMENT!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Certains portails ont encore des problèmes" -ForegroundColor Yellow
}
