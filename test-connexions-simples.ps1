# Test simple des connexions NafaPlace
Write-Host "=== Test des Connexions NafaPlace ===" -ForegroundColor Green

# Test 1: Connexion Admin
Write-Host "`n--- Test Connexion Admin ---" -ForegroundColor Yellow
try {
    $adminBody = @{
        email = "<EMAIL>"
        password = "Admin123!"
    } | ConvertTo-Json

    $adminResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/login" -Method POST -Body $adminBody -ContentType "application/json"
    Write-Host "✅ Connexion Admin réussie!" -ForegroundColor Green
    Write-Host "Token: $($adminResponse.token.Substring(0, 50))..." -ForegroundColor Gray
    Write-Host "Username: $($adminResponse.username)" -ForegroundColor Gray
    $adminToken = $adminResponse.token
} catch {
    Write-Host "❌ Échec connexion Admin: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Détails: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

# Test 2: Création compte utilisateur
Write-Host "`n--- Test Création Utilisateur ---" -ForegroundColor Yellow
try {
    $userBody = @{
        email = "testuser$(Get-Random)@nafaplace.com"
        username = "testuser$(Get-Random)"
        password = "User123!"
        firstName = "Test"
        lastName = "User"
    } | ConvertTo-Json

    $userResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/register" -Method POST -Body $userBody -ContentType "application/json"
    Write-Host "✅ Création utilisateur réussie!" -ForegroundColor Green
    Write-Host "Username: $($userResponse.username)" -ForegroundColor Gray
    Write-Host "Email: $($userResponse.email)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Échec création utilisateur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Détails erreur: $errorContent" -ForegroundColor Red
    }
}

# Test 3: Création compte vendeur
Write-Host "`n--- Test Création Vendeur ---" -ForegroundColor Yellow
try {
    $sellerBody = @{
        email = "testseller$(Get-Random)@nafaplace.com"
        username = "testseller$(Get-Random)"
        password = "Seller123!"
        firstName = "Test"
        lastName = "Seller"
        companyName = "Test Company"
        phoneNumber = "+224123456789"
    } | ConvertTo-Json

    $sellerResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/auth/register-seller" -Method POST -Body $sellerBody -ContentType "application/json"
    Write-Host "✅ Création vendeur réussie!" -ForegroundColor Green
    Write-Host "Username: $($sellerResponse.username)" -ForegroundColor Gray
    Write-Host "Company: $($sellerResponse.companyName)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Échec création vendeur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Détails erreur: $errorContent" -ForegroundColor Red
    }
}

# Test 4: Test fonctionnalités Admin (si connecté)
if ($adminToken) {
    Write-Host "`n--- Test Fonctionnalités Admin ---" -ForegroundColor Yellow
    
    try {
        $headers = @{ "Authorization" = "Bearer $adminToken" }
        
        # Récupérer profil admin
        $profileResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/users/profile" -Headers $headers
        Write-Host "✅ Profil admin récupéré: $($profileResponse.username)" -ForegroundColor Green
        
        # Récupérer tous les utilisateurs
        $usersResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/users" -Headers $headers
        Write-Host "✅ Utilisateurs récupérés: $($usersResponse.Count) utilisateurs" -ForegroundColor Green
        
        # Récupérer tous les rôles
        $rolesResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/identity/roles" -Headers $headers
        Write-Host "✅ Rôles récupérés: $($rolesResponse.Count) rôles" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Échec test fonctionnalités admin: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 5: Test fonctionnalités Catalog
Write-Host "`n--- Test Fonctionnalités Catalog ---" -ForegroundColor Yellow
try {
    # Récupérer produits
    $productsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/catalog/v1/products"
    Write-Host "✅ Produits récupérés: $($productsResponse.Count) produits" -ForegroundColor Green
    
    # Récupérer catégories
    $categoriesResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/catalog/v1/categories"
    Write-Host "✅ Catégories récupérées: $($categoriesResponse.Count) catégories" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Échec test catalog: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Tests terminés ===" -ForegroundColor Green
Write-Host "Vous pouvez maintenant tester manuellement les portails web dans votre navigateur:" -ForegroundColor White
Write-Host "- Main Web: http://localhost:8080" -ForegroundColor Cyan
Write-Host "- Admin Portal: http://localhost:8081" -ForegroundColor Cyan  
Write-Host "- Seller Portal: http://localhost:8082" -ForegroundColor Cyan
