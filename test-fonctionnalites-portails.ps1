# Script de test des fonctionnalités des portails NafaPlace
param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== Test des Fonctionnalités NafaPlace ===" -ForegroundColor Green

# Variables globales
$adminToken = ""
$sellerToken = ""
$userToken = ""

# Fonction pour faire des requêtes HTTP
function Invoke-ApiRequest {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [object]$Body = $null,
        [hashtable]$Headers = @{},
        [string]$ContentType = "application/json"
    )
    
    try {
        $requestParams = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            ContentType = $ContentType
        }
        
        if ($Body -and $Method -ne "GET") {
            if ($Body -is [string]) {
                $requestParams.Body = $Body
            } else {
                $requestParams.Body = $Body | ConvertTo-Json -Depth 10
            }
        }
        
        $response = Invoke-RestMethod @requestParams
        return @{
            Success = $true
            Data = $response
            StatusCode = 200
        }
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = $statusCode
        }
    }
}

# Test 1: Connexion Admin
Write-Host "`n=== 1. TEST CONNEXION ADMIN ===" -ForegroundColor Yellow
$adminLoginData = @{
    email = "<EMAIL>"
    password = "Admin123!"
}

$adminLoginResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/auth/login" -Method "POST" -Body $adminLoginData
if ($adminLoginResult.Success) {
    $adminToken = $adminLoginResult.Data.token
    Write-Host "✅ Connexion Admin réussie" -ForegroundColor Green
    Write-Host "Token: $($adminToken.Substring(0, 20))..." -ForegroundColor Gray
} else {
    Write-Host "❌ Échec connexion Admin: $($adminLoginResult.Error)" -ForegroundColor Red
}

# Test 2: Création d'un compte utilisateur
Write-Host "`n=== 2. TEST CRÉATION COMPTE UTILISATEUR ===" -ForegroundColor Yellow
$newUserData = @{
    email = "<EMAIL>"
    username = "testuser"
    password = "User123!"
    firstName = "Test"
    lastName = "User"
}

$createUserResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/auth/register" -Method "POST" -Body $newUserData
if ($createUserResult.Success) {
    Write-Host "✅ Création compte utilisateur réussie" -ForegroundColor Green
    Write-Host "Utilisateur créé: $($createUserResult.Data.username)" -ForegroundColor Gray
} else {
    Write-Host "❌ Échec création utilisateur: $($createUserResult.Error)" -ForegroundColor Red
}

# Test 3: Connexion du nouvel utilisateur
Write-Host "`n=== 3. TEST CONNEXION UTILISATEUR ===" -ForegroundColor Yellow
$userLoginData = @{
    email = "<EMAIL>"
    password = "User123!"
}

$userLoginResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/auth/login" -Method "POST" -Body $userLoginData
if ($userLoginResult.Success) {
    $userToken = $userLoginResult.Data.token
    Write-Host "✅ Connexion utilisateur réussie" -ForegroundColor Green
} else {
    Write-Host "❌ Échec connexion utilisateur: $($userLoginResult.Error)" -ForegroundColor Red
}

# Test 4: Création d'un compte vendeur
Write-Host "`n=== 4. TEST CRÉATION COMPTE VENDEUR ===" -ForegroundColor Yellow
$newSellerData = @{
    email = "<EMAIL>"
    username = "testseller"
    password = "Seller123!"
    firstName = "Test"
    lastName = "Seller"
    companyName = "Test Company"
    phoneNumber = "+224123456789"
}

$createSellerResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/auth/register-seller" -Method "POST" -Body $newSellerData
if ($createSellerResult.Success) {
    Write-Host "✅ Création compte vendeur réussie" -ForegroundColor Green
    Write-Host "Vendeur créé: $($createSellerResult.Data.username)" -ForegroundColor Gray
} else {
    Write-Host "❌ Échec création vendeur: $($createSellerResult.Error)" -ForegroundColor Red
}

# Test 5: Connexion du vendeur
Write-Host "`n=== 5. TEST CONNEXION VENDEUR ===" -ForegroundColor Yellow
$sellerLoginData = @{
    email = "<EMAIL>"
    password = "Seller123!"
}

$sellerLoginResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/auth/login" -Method "POST" -Body $sellerLoginData
if ($sellerLoginResult.Success) {
    $sellerToken = $sellerLoginResult.Data.token
    Write-Host "✅ Connexion vendeur réussie" -ForegroundColor Green
} else {
    Write-Host "❌ Échec connexion vendeur: $($sellerLoginResult.Error)" -ForegroundColor Red
}

# Test 6: Test des fonctionnalités Admin
Write-Host "`n=== 6. TEST FONCTIONNALITÉS ADMIN ===" -ForegroundColor Yellow
if ($adminToken) {
    $adminHeaders = @{ "Authorization" = "Bearer $adminToken" }
    
    # Récupérer tous les utilisateurs
    $usersResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/users" -Headers $adminHeaders
    if ($usersResult.Success) {
        Write-Host "✅ Récupération des utilisateurs réussie ($($usersResult.Data.Count) utilisateurs)" -ForegroundColor Green
    } else {
        Write-Host "❌ Échec récupération utilisateurs: $($usersResult.Error)" -ForegroundColor Red
    }
    
    # Récupérer tous les rôles
    $rolesResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/roles" -Headers $adminHeaders
    if ($rolesResult.Success) {
        Write-Host "✅ Récupération des rôles réussie ($($rolesResult.Data.Count) rôles)" -ForegroundColor Green
    } else {
        Write-Host "❌ Échec récupération rôles: $($rolesResult.Error)" -ForegroundColor Red
    }
    
    # Récupérer le profil admin
    $adminProfileResult = Invoke-ApiRequest -Url "$BaseUrl/api/identity/users/profile" -Headers $adminHeaders
    if ($adminProfileResult.Success) {
        Write-Host "✅ Récupération profil admin réussie: $($adminProfileResult.Data.username)" -ForegroundColor Green
    } else {
        Write-Host "❌ Échec récupération profil admin: $($adminProfileResult.Error)" -ForegroundColor Red
    }
}

# Test 7: Test des fonctionnalités Catalog
Write-Host "`n=== 7. TEST FONCTIONNALITÉS CATALOG ===" -ForegroundColor Yellow

# Récupérer les produits
$productsResult = Invoke-ApiRequest -Url "$BaseUrl/api/catalog/v1/products"
if ($productsResult.Success) {
    Write-Host "✅ Récupération des produits réussie ($($productsResult.Data.Count) produits)" -ForegroundColor Green
} else {
    Write-Host "❌ Échec récupération produits: $($productsResult.Error)" -ForegroundColor Red
}

# Récupérer les catégories
$categoriesResult = Invoke-ApiRequest -Url "$BaseUrl/api/catalog/v1/categories"
if ($categoriesResult.Success) {
    Write-Host "✅ Récupération des catégories réussie ($($categoriesResult.Data.Count) catégories)" -ForegroundColor Green
} else {
    Write-Host "❌ Échec récupération catégories: $($categoriesResult.Error)" -ForegroundColor Red
}

# Test 8: Test création de produit (si vendeur connecté)
Write-Host "`n=== 8. TEST CRÉATION PRODUIT ===" -ForegroundColor Yellow
if ($sellerToken) {
    $sellerHeaders = @{ "Authorization" = "Bearer $sellerToken" }
    
    $newProductData = @{
        name = "Produit Test"
        description = "Description du produit test"
        price = 25000
        categoryId = 1
        sellerId = 1
        stock = 10
        isActive = $true
    }
    
    $createProductResult = Invoke-ApiRequest -Url "$BaseUrl/api/catalog/v1/products" -Method "POST" -Body $newProductData -Headers $sellerHeaders
    if ($createProductResult.Success) {
        Write-Host "✅ Création produit réussie: $($createProductResult.Data.name)" -ForegroundColor Green
    } else {
        Write-Host "❌ Échec création produit: $($createProductResult.Error)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ Pas de token vendeur - Test création produit ignoré" -ForegroundColor Yellow
}

Write-Host "`n=== RÉSUMÉ DES TESTS ===" -ForegroundColor Green
Write-Host "Tests terminés. Vérifiez les résultats ci-dessus." -ForegroundColor White
Write-Host "Pour des tests manuels détaillés, utilisez les portails web ouverts dans votre navigateur." -ForegroundColor Gray
