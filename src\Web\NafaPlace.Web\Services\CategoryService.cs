using System.Net.Http.Json;
using NafaPlace.Web.Models.Catalog;

namespace NafaPlace.Web.Services;

public class CategoryService : ICategoryService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiBaseUrl;
    private readonly List<CategoryDto> _mockCategories;

    public CategoryService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _apiBaseUrl = httpClient.BaseAddress?.ToString().TrimEnd('/') ?? "http://localhost:5243";
        
        // Données simulées pour les tests ou en cas d'indisponibilité de l'API
        _mockCategories = new List<CategoryDto>
        {
            new CategoryDto
            {
                Id = 1,
                Name = "Électronique",
                Description = "Produits électroniques et gadgets",
                ImageUrl = "/images/categories/electronics.jpg",
                ParentId = null
            },
            new CategoryDto
            {
                Id = 2,
                Name = "Smartphones",
                Description = "Téléphones mobiles et accessoires",
                ImageUrl = "/images/categories/smartphones.jpg",
                ParentId = 1
            },
            new CategoryDto
            {
                Id = 3,
                Name = "Ordinateurs",
                Description = "Ordinateurs portables et de bureau",
                ImageUrl = "/images/categories/computers.jpg",
                ParentId = 1
            },
            new CategoryDto
            {
                Id = 4,
                Name = "Mode",
                Description = "Vêtements, chaussures et accessoires",
                ImageUrl = "/images/categories/fashion.jpg",
                ParentId = null
            },
            new CategoryDto
            {
                Id = 5,
                Name = "Maison",
                Description = "Meubles et décoration",
                ImageUrl = "/images/categories/home.jpg",
                ParentId = null
            }
        };
    }

    public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync()
    {
        try
        {
            // Essayer d'abord de récupérer depuis l'API
            Console.WriteLine("Tentative de récupération des catégories depuis l'API...");
            var categories = await _httpClient.GetFromJsonAsync<IEnumerable<CategoryDto>>("v1/categories");
            Console.WriteLine($"Catégories récupérées avec succès: {categories?.Count() ?? 0} catégories");
            return categories ?? _mockCategories;
        }
        catch (Exception ex)
        {
            // En cas d'erreur, retourner les données simulées
            Console.WriteLine($"Erreur lors de la récupération des catégories: {ex.Message}");
            return _mockCategories;
        }
    }

    public async Task<CategoryDto> GetCategoryByIdAsync(int id)
    {
        try
        {
            // Essayer d'abord de récupérer depuis l'API
            Console.WriteLine($"Tentative de récupération de la catégorie {id} depuis l'API...");
            var category = await _httpClient.GetFromJsonAsync<CategoryDto>($"v1/categories/{id}");
            Console.WriteLine($"Catégorie {id} récupérée avec succès");
            return category ?? new CategoryDto { Id = id, Name = "Catégorie inconnue" };
        }
        catch (Exception ex)
        {
            // En cas d'erreur, retourner la catégorie simulée correspondante
            Console.WriteLine($"Erreur lors de la récupération de la catégorie {id}: {ex.Message}");
            var category = _mockCategories.FirstOrDefault(c => c.Id == id);
            return category ?? new CategoryDto { Id = id, Name = "Catégorie inconnue" };
        }
    }

    public async Task<IEnumerable<CategoryDto>> GetChildCategoriesAsync(int parentId)
    {
        try
        {
            // Essayer d'abord de récupérer depuis l'API
            Console.WriteLine($"Tentative de récupération des sous-catégories de {parentId} depuis l'API...");
            var categories = await _httpClient.GetFromJsonAsync<IEnumerable<CategoryDto>>($"v1/categories/{parentId}/subcategories");
            Console.WriteLine($"Sous-catégories de {parentId} récupérées avec succès: {categories?.Count() ?? 0} catégories");
            return categories ?? _mockCategories.Where(c => c.ParentId == parentId).ToList();
        }
        catch (Exception ex)
        {
            // En cas d'erreur, retourner les catégories enfants simulées
            Console.WriteLine($"Erreur lors de la récupération des sous-catégories de {parentId}: {ex.Message}");
            return _mockCategories.Where(c => c.ParentId == parentId).ToList();
        }
    }

    public string GetImageUrl(CategoryDto category, bool thumbnail = false)
    {
        try
        {
            if (category == null)
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            var url = category.ImageUrl;

            if (string.IsNullOrEmpty(url))
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            // Si l'URL est déjà complète (http/https) ou est une data URL, la retourner telle quelle
            if (url.StartsWith("http://") || url.StartsWith("https://") || url.StartsWith("data:"))
            {
                return url;
            }

            // Sinon, construire l'URL complète avec l'API base URL
            return _apiBaseUrl + (url.StartsWith("/") ? url : $"/{url}");
        }
        catch (Exception)
        {
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
    }
}
