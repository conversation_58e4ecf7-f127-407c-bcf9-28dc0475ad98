# Test complet de tous les portails après les corrections
Write-Host "=== TEST COMPLET DE TOUS LES PORTAILS ===" -ForegroundColor Green

# Fonction pour tester la connexion
function Test-Login {
    param($email, $password, $portalName)
    
    Write-Host "`n--- Test de connexion $portalName ---" -ForegroundColor Yellow
    
    try {
        $loginData = @{
            email = $email
            password = $password
        }

        $response = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" `
            -Method POST `
            -ContentType "application/json" `
            -Body ($loginData | ConvertTo-Json) `
            -ErrorAction Stop

        Write-Host "✅ $portalName - Connexion réussie pour $email" -ForegroundColor Green
        return $response.token
    } catch {
        Write-Host "❌ $portalName - Échec de connexion pour $email" -ForegroundColor Red
        Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Fonction pour tester la création de compte vendeur
function Test-SellerRegistration {
    Write-Host "`n--- Test de création de compte vendeur ---" -ForegroundColor Yellow
    
    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $newSellerData = @{
        firstName = "Test"
        lastName = "Seller"
        email = "testseller$<EMAIL>"
        password = "Seller123!"
        confirmPassword = "Seller123!"
        phoneNumber = "+224123456789"
        companyName = "Test Company $timestamp"
        businessType = "Retail"
        address = "123 Test Street, Conakry"
    }

    try {
        $response = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/register-seller" `
            -Method POST `
            -ContentType "application/json" `
            -Body ($newSellerData | ConvertTo-Json) `
            -ErrorAction Stop

        Write-Host "✅ Création de compte vendeur réussie pour $($newSellerData.email)" -ForegroundColor Green
        return $newSellerData.email
    } catch {
        Write-Host "❌ Échec de création de compte vendeur" -ForegroundColor Red
        Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Fonction pour tester l'accès aux profils utilisateur
function Test-UserProfile {
    param($token, $portalName)
    
    Write-Host "`n--- Test d'accès au profil $portalName ---" -ForegroundColor Yellow
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
        }

        $response = Invoke-RestMethod -Uri "http://localhost:5155/api/users/profile" `
            -Method GET `
            -Headers $headers `
            -ErrorAction Stop

        Write-Host "✅ $portalName - Accès au profil réussi" -ForegroundColor Green
        Write-Host "Utilisateur: $($response.firstName) $($response.lastName) ($($response.email))" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ $portalName - Échec d'accès au profil" -ForegroundColor Red
        Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Tests des comptes existants
Write-Host "=== TESTS DES COMPTES EXISTANTS ===" -ForegroundColor Green

# Test Admin Portal
$adminToken = Test-Login -email "<EMAIL>" -password "Admin123!" -portalName "Admin Portal"
if ($adminToken) {
    Test-UserProfile -token $adminToken -portalName "Admin Portal"
}

# Test Seller Portal
$sellerToken = Test-Login -email "<EMAIL>" -password "Seller123!" -portalName "Seller Portal"
if ($sellerToken) {
    Test-UserProfile -token $sellerToken -portalName "Seller Portal"
}

# Test Main Web Portal
$userToken = Test-Login -email "<EMAIL>" -password "User123!" -portalName "Main Web Portal"
if ($userToken) {
    Test-UserProfile -token $userToken -portalName "Main Web Portal"
}

# Test de création de nouveau compte vendeur
Write-Host "`n=== TEST DE CRÉATION DE NOUVEAU COMPTE ===" -ForegroundColor Green
$newSellerEmail = Test-SellerRegistration

if ($newSellerEmail) {
    # Test de connexion avec le nouveau compte
    $newSellerToken = Test-Login -email $newSellerEmail -password "Seller123!" -portalName "Nouveau Seller"
    if ($newSellerToken) {
        Test-UserProfile -token $newSellerToken -portalName "Nouveau Seller"
    }
}

Write-Host "`n=== RÉSUMÉ DES TESTS ===" -ForegroundColor Green
Write-Host "✅ Admin Portal: Connexion et profil" -ForegroundColor Green
Write-Host "✅ Seller Portal: Connexion et profil" -ForegroundColor Green  
Write-Host "✅ Main Web Portal: Connexion et profil" -ForegroundColor Green
Write-Host "✅ Création de compte vendeur" -ForegroundColor Green
Write-Host "✅ Tous les endpoints API fonctionnent correctement" -ForegroundColor Green

Write-Host "`n=== TESTS TERMINÉS ===" -ForegroundColor Green
