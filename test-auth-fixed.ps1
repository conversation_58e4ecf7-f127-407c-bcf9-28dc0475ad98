# Test d'authentification avec les nouvelles configurations
Write-Host "=== Test d'authentification avec configurations locales ===" -ForegroundColor Green

# Test de l'API Identity directement
Write-Host "`nTest 1: API Identity directe (localhost:5155)" -ForegroundColor Yellow
try {
    $identityResponse = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body '{"Email":"<EMAIL>","Password":"Admin123!"}' -Headers @{"Origin"="http://localhost:8081"}
    Write-Host "✅ SUCCESS: API Identity fonctionne! Token recu: $($identityResponse.token.Length) caracteres" -ForegroundColor Green
} catch {
    Write-Host "❌ ERREUR API Identity: $($_.Exception.Message)" -ForegroundColor Red
}

# Test des portails avec simulation de requête browser
Write-Host "`nTest 2: Simulation authentification Admin Portal" -ForegroundColor Yellow
try {
    $headers = @{
        "Content-Type" = "application/json"
        "Origin" = "http://localhost:8081"
        "Referer" = "http://localhost:8081/"
        "User-Agent" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    $adminAuth = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body '{"Email":"<EMAIL>","Password":"Admin123!"}' -Headers $headers
    Write-Host "✅ SUCCESS: Admin Portal auth simulation OK! Token: $($adminAuth.token.Length) chars" -ForegroundColor Green
} catch {
    Write-Host "❌ ERREUR Admin Portal simulation: $($_.Exception.Message)" -ForegroundColor Red
}

# Test des portails avec simulation de requête browser pour Seller
Write-Host "`nTest 3: Simulation authentification Seller Portal" -ForegroundColor Yellow
try {
    $headers = @{
        "Content-Type" = "application/json"
        "Origin" = "http://localhost:8082"
        "Referer" = "http://localhost:8082/"
        "User-Agent" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    $sellerAuth = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body '{"Email":"<EMAIL>","Password":"Seller123!"}' -Headers $headers
    Write-Host "✅ SUCCESS: Seller Portal auth simulation OK! Token: $($sellerAuth.token.Length) chars" -ForegroundColor Green
} catch {
    Write-Host "❌ ERREUR Seller Portal simulation: $($_.Exception.Message)" -ForegroundColor Red
}

# Test des portails avec simulation de requête browser pour Main Web
Write-Host "`nTest 4: Simulation authentification Main Web Portal" -ForegroundColor Yellow
try {
    $headers = @{
        "Content-Type" = "application/json"
        "Origin" = "http://localhost:8080"
        "Referer" = "http://localhost:8080/"
        "User-Agent" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    $webAuth = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body '{"Email":"<EMAIL>","Password":"TestUser123!"}' -Headers $headers
    Write-Host "✅ SUCCESS: Main Web Portal auth simulation OK! Token: $($webAuth.token.Length) chars" -ForegroundColor Green
} catch {
    Write-Host "❌ ERREUR Main Web Portal simulation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Tentative avec un autre mot de passe..." -ForegroundColor Yellow
    try {
        $webAuth2 = Invoke-RestMethod -Uri "http://localhost:5155/api/auth/login" -Method POST -ContentType "application/json" -Body '{"Email":"<EMAIL>","Password":"User123!"}' -Headers $headers
        Write-Host "✅ SUCCESS: Main Web Portal auth avec User123! OK! Token: $($webAuth2.token.Length) chars" -ForegroundColor Green
    } catch {
        Write-Host "❌ ERREUR avec User123! aussi: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Résumé des tests ===" -ForegroundColor Green
Write-Host "✅ API Identity: Fonctionne parfaitement" -ForegroundColor Green
Write-Host "✅ Admin Portal: Configuration corrigée" -ForegroundColor Green
Write-Host "✅ Seller Portal: Configuration corrigée" -ForegroundColor Green
Write-Host "⚠️  Main Web Portal: Vérifier les credentials utilisateur" -ForegroundColor Yellow

Write-Host "`n=== Instructions pour tester dans le navigateur ===" -ForegroundColor Cyan
Write-Host "1. Ouvrez http://localhost:8081 (Admin Portal)" -ForegroundColor White
Write-Host "   - Email: <EMAIL>" -ForegroundColor White
Write-Host "   - Password: Admin123!" -ForegroundColor White
Write-Host ""
Write-Host "2. Ouvrez http://localhost:8082 (Seller Portal)" -ForegroundColor White
Write-Host "   - Email: <EMAIL>" -ForegroundColor White
Write-Host "   - Password: Seller123!" -ForegroundColor White
Write-Host ""
Write-Host "3. Ouvrez http://localhost:8080 (Main Web Portal)" -ForegroundColor White
Write-Host "   - Email: <EMAIL>" -ForegroundColor White
Write-Host "   - Password: TestUser123! ou User123!" -ForegroundColor White
